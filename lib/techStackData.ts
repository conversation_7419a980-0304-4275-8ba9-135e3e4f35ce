// Technology details with descriptions, pros, and cons
export const technologyDetails = {
  // Frontend Technologies
  "Next.js": {
    description: "A React framework that enables functionality such as server-side rendering and generating static websites for React based web applications.",
    pros: ["Server-side rendering", "Static site generation", "Built-in routing", "API routes", "Excellent performance"],
    cons: ["Learning curve", "Can be overkill for simple projects", "Vendor lock-in with Vercel"]
  },
  "React": {
    description: "A JavaScript library for building user interfaces, particularly web applications with dynamic, interactive elements.",
    pros: ["Large ecosystem", "Component-based", "Virtual DOM", "Strong community", "Flexible"],
    cons: ["Steep learning curve", "Rapid changes", "JSX syntax", "Only handles UI"]
  },
  "Vue.js": {
    description: "A progressive JavaScript framework for building user interfaces and single-page applications.",
    pros: ["Easy to learn", "Great documentation", "Flexible", "Small bundle size", "Good performance"],
    cons: ["Smaller ecosystem than React", "Less job opportunities", "Language barriers in community"]
  },
  "Angular": {
    description: "A TypeScript-based open-source web application framework led by the Angular Team at Google.",
    pros: ["Full framework", "TypeScript by default", "Powerful CLI", "Enterprise-ready", "Two-way data binding"],
    cons: ["Steep learning curve", "Verbose", "Large bundle size", "Complex for small projects"]
  },
  "Svelte": {
    description: "A radical new approach to building user interfaces with a compile-time optimized framework.",
    pros: ["No virtual DOM", "Small bundle size", "Easy to learn", "Great performance", "Less boilerplate"],
    cons: ["Smaller ecosystem", "Limited tooling", "Less mature", "Fewer learning resources"]
  },
  "TypeScript": {
    description: "A strongly typed programming language that builds on JavaScript by adding static type definitions.",
    pros: ["Type safety", "Better IDE support", "Catches errors early", "Great for large projects", "Excellent tooling"],
    cons: ["Learning curve", "Compilation step", "Can be verbose", "Setup complexity"]
  },
  "Tailwind CSS": {
    description: "A utility-first CSS framework packed with classes that can be composed to build any design.",
    pros: ["Utility-first", "Highly customizable", "Small production builds", "Consistent design", "Fast development"],
    cons: ["Learning curve", "HTML can look cluttered", "Requires purging", "Less semantic"]
  },
  "Styled Components": {
    description: "A library for React and React Native that allows you to use component-level styles written with a mixture of JavaScript and CSS.",
    pros: ["CSS-in-JS", "Dynamic styling", "Automatic vendor prefixing", "No class name bugs", "Component-based"],
    cons: ["Runtime overhead", "Learning curve", "Bundle size", "Debugging complexity"]
  },
  "Zustand": {
    description: "A small, fast and scalable bearbones state-management solution using simplified flux principles.",
    pros: ["Simple API", "Small bundle size", "No boilerplate", "TypeScript support", "Framework agnostic"],
    cons: ["Less mature", "Smaller ecosystem", "Limited devtools", "Less documentation"]
  },
  "Redux Toolkit": {
    description: "The official, opinionated, batteries-included toolset for efficient Redux development.",
    pros: ["Predictable state", "Time-travel debugging", "Large ecosystem", "Well-tested patterns", "Great devtools"],
    cons: ["Boilerplate code", "Learning curve", "Can be overkill", "Complexity for simple apps"]
  },

  // Backend Technologies
  "Node.js": {
    description: "A JavaScript runtime built on Chrome's V8 JavaScript engine for building scalable network applications.",
    pros: ["JavaScript everywhere", "Large ecosystem", "Fast development", "Great for real-time apps", "NPM packages"],
    cons: ["Single-threaded", "Callback complexity", "Not ideal for CPU-intensive tasks", "Rapid changes"]
  },
  "Express.js": {
    description: "A minimal and flexible Node.js web application framework that provides robust features for web and mobile applications.",
    pros: ["Minimalist", "Flexible", "Large ecosystem", "Easy to learn", "Great middleware support"],
    cons: ["Minimal by default", "No built-in structure", "Security concerns", "Callback hell potential"]
  },
  "Python/Django": {
    description: "A high-level Python web framework that encourages rapid development and clean, pragmatic design.",
    pros: ["Batteries included", "Admin interface", "ORM", "Security features", "Rapid development"],
    cons: ["Monolithic", "Learning curve", "Performance limitations", "Less flexible"]
  },
  "Python/FastAPI": {
    description: "A modern, fast web framework for building APIs with Python 3.6+ based on standard Python type hints.",
    pros: ["Fast performance", "Automatic API docs", "Type hints", "Async support", "Easy to learn"],
    cons: ["Newer framework", "Smaller ecosystem", "Less mature", "Limited resources"]
  },
  "Ruby on Rails": {
    description: "A server-side web application framework written in Ruby under the MIT License.",
    pros: ["Convention over configuration", "Rapid development", "Great community", "Mature ecosystem", "Developer happiness"],
    cons: ["Performance concerns", "Magic can be confusing", "Monolithic", "Learning curve"]
  },
  "PHP/Laravel": {
    description: "A web application framework with expressive, elegant syntax designed to make development enjoyable and creative.",
    pros: ["Elegant syntax", "Great documentation", "Built-in features", "Large community", "Easy deployment"],
    cons: ["PHP stigma", "Performance concerns", "Monolithic", "Security concerns if not careful"]
  },
  "Java/Spring": {
    description: "A comprehensive programming and configuration model for modern Java-based enterprise applications.",
    pros: ["Enterprise-ready", "Mature ecosystem", "Strong typing", "Great tooling", "Scalable"],
    cons: ["Verbose", "Complex configuration", "Learning curve", "Heavy framework"]
  },
  "GraphQL": {
    description: "A query language for APIs and a runtime for fulfilling those queries with existing data.",
    pros: ["Single endpoint", "Type system", "Efficient data fetching", "Strong tooling", "Self-documenting"],
    cons: ["Learning curve", "Caching complexity", "Over-fetching potential", "N+1 query problem"]
  },

  // Database Technologies
  "PostgreSQL": {
    description: "A powerful, open source object-relational database system with strong reputation for reliability and performance.",
    pros: ["ACID compliance", "Advanced features", "Extensible", "Strong consistency", "Great performance"],
    cons: ["Memory usage", "Complexity", "Learning curve", "Slower for simple queries"]
  },
  "MySQL": {
    description: "An open-source relational database management system based on SQL – Structured Query Language.",
    pros: ["Easy to use", "Fast", "Reliable", "Large community", "Good documentation"],
    cons: ["Limited features", "Storage engine complexity", "Replication lag", "Less advanced than PostgreSQL"]
  },
  "MongoDB": {
    description: "A source-available cross-platform document-oriented database program classified as a NoSQL database.",
    pros: ["Flexible schema", "Horizontal scaling", "JSON-like documents", "Fast development", "Good for unstructured data"],
    cons: ["Memory usage", "Data consistency", "Complex queries", "Learning curve"]
  },
  "Redis": {
    description: "An in-memory data structure store, used as a database, cache, and message broker.",
    pros: ["Very fast", "Versatile data structures", "Pub/Sub messaging", "Atomic operations", "Great for caching"],
    cons: ["Memory-only", "Data persistence complexity", "Single-threaded", "Memory limitations"]
  },
  "Supabase": {
    description: "An open source Firebase alternative providing a Postgres database, Authentication, instant APIs, and Realtime subscriptions.",
    pros: ["Open source", "PostgreSQL-based", "Real-time features", "Built-in auth", "Easy to use"],
    cons: ["Newer platform", "Limited customization", "Vendor lock-in", "Less mature ecosystem"]
  },

  // Authentication
  "NextAuth.js": {
    description: "A complete open source authentication solution for Next.js applications.",
    pros: ["Easy integration", "Multiple providers", "Secure by default", "TypeScript support", "Flexible"],
    cons: ["Next.js specific", "Limited customization", "Database sessions complexity", "Learning curve"]
  },
  "Auth0": {
    description: "A flexible, drop-in solution to add authentication and authorization services to applications.",
    pros: ["Enterprise features", "Multiple protocols", "Easy integration", "Scalable", "Great documentation"],
    cons: ["Pricing", "Vendor lock-in", "Complexity for simple use cases", "Limited customization"]
  },
  "Firebase Auth": {
    description: "A service that can authenticate users using only client-side code, handling the backend automatically.",
    pros: ["Easy setup", "Multiple providers", "Real-time", "Scalable", "Google integration"],
    cons: ["Vendor lock-in", "Limited customization", "Pricing model", "Google dependency"]
  },
  "Clerk": {
    description: "A complete suite of embeddable UIs, flexible APIs, and admin dashboards to authenticate and manage users.",
    pros: ["Modern UI", "Easy integration", "Great DX", "Comprehensive features", "Good documentation"],
    cons: ["Newer platform", "Pricing", "Limited customization", "Vendor lock-in"]
  },

  // Deployment
  "Vercel": {
    description: "A cloud platform for static sites and Serverless Functions that fits perfectly with your workflow.",
    pros: ["Easy deployment", "Great DX", "Edge network", "Automatic scaling", "Git integration"],
    cons: ["Vendor lock-in", "Pricing for scale", "Limited backend features", "Cold starts"]
  },
  "Netlify": {
    description: "A web developer platform that multiplies productivity by unifying the elements of the modern decoupled web.",
    pros: ["Easy deployment", "Great for static sites", "Built-in CDN", "Form handling", "Split testing"],
    cons: ["Limited backend", "Function limitations", "Pricing for scale", "Build time limits"]
  },
  "AWS": {
    description: "Amazon Web Services offers reliable, scalable, and inexpensive cloud computing services.",
    pros: ["Comprehensive services", "Scalable", "Reliable", "Global infrastructure", "Enterprise-ready"],
    cons: ["Complex", "Learning curve", "Pricing complexity", "Vendor lock-in"]
  },
  "Google Cloud": {
    description: "A suite of cloud computing services that runs on the same infrastructure that Google uses internally.",
    pros: ["Good pricing", "Strong AI/ML services", "Kubernetes native", "Global network", "Innovation"],
    cons: ["Smaller ecosystem", "Less mature", "Learning curve", "Service discontinuation risk"]
  },
  "DigitalOcean": {
    description: "A cloud infrastructure provider focused on simplifying web infrastructure for software developers.",
    pros: ["Simple pricing", "Easy to use", "Good documentation", "Developer-friendly", "Predictable costs"],
    cons: ["Limited services", "Less enterprise features", "Smaller scale", "Limited global presence"]
  }
};

export const techStackData = {
  saasTypes: {
    ecommerce: {
      name: "E-commerce Platform",
      description: "Online store with product catalog, shopping cart, and payment processing",
      techStack: {
        frontend: {
          primary: ["Next.js", "React", "TypeScript"],
          styling: ["Tailwind CSS", "Styled Components"],
          stateManagement: ["Zustand", "Redux Toolkit"],
          alternatives: ["Vue.js", "Angular", "Svelte"]
        },
        backend: {
          primary: ["Node.js", "Express.js"],
          alternatives: ["Python/Django", "Ruby on Rails", "PHP/Laravel", "Java/Spring"]
        },
        database: {
          primary: ["PostgreSQL"],
          alternatives: ["MySQL", "MongoDB", "Supabase"]
        },
        auth: {
          primary: ["NextAuth.js", "Auth0"],
          alternatives: ["Firebase Auth", "Supabase Auth", "Clerk"]
        },
        payments: {
          primary: ["Stripe", "PayPal"],
          alternatives: ["Square", "Braintree"]
        },
        deployment: {
          primary: ["Vercel", "Netlify"],
          alternatives: ["AWS", "Google Cloud", "DigitalOcean"]
        },
        storage: {
          primary: ["AWS S3", "Cloudinary"],
          alternatives: ["Google Cloud Storage", "Azure Blob"]
        }
      }
    },
    crm: {
      name: "Customer Relationship Management",
      description: "Manage customer interactions, sales pipeline, and business relationships",
      techStack: {
        frontend: {
          primary: ["React", "TypeScript", "Next.js"],
          styling: ["Tailwind CSS", "Material-UI"],
          stateManagement: ["React Query", "Zustand"],
          alternatives: ["Vue.js", "Angular"]
        },
        backend: {
          primary: ["Node.js", "Express.js", "GraphQL"],
          alternatives: ["Python/FastAPI", "C#/.NET", "Java/Spring"]
        },
        database: {
          primary: ["PostgreSQL", "Redis"],
          alternatives: ["MySQL", "MongoDB"]
        },
        auth: {
          primary: ["Auth0", "Firebase Auth"],
          alternatives: ["Okta", "AWS Cognito"]
        },
        realtime: {
          primary: ["Socket.io", "WebSockets"],
          alternatives: ["Pusher", "Ably"]
        },
        deployment: {
          primary: ["AWS", "Docker"],
          alternatives: ["Google Cloud", "Azure"]
        },
        analytics: {
          primary: ["Mixpanel", "Amplitude"],
          alternatives: ["Google Analytics", "PostHog"]
        }
      }
    },
    analytics: {
      name: "Analytics Dashboard",
      description: "Data visualization and business intelligence platform",
      techStack: {
        frontend: {
          primary: ["React", "D3.js", "Chart.js"],
          styling: ["Tailwind CSS", "Ant Design"],
          stateManagement: ["Redux Toolkit", "React Query"],
          alternatives: ["Vue.js", "Angular"]
        },
        backend: {
          primary: ["Python", "FastAPI", "Pandas"],
          alternatives: ["Node.js", "R/Shiny", "Scala/Spark"]
        },
        database: {
          primary: ["PostgreSQL", "ClickHouse", "Redis"],
          alternatives: ["BigQuery", "Snowflake", "MongoDB"]
        },
        dataProcessing: {
          primary: ["Apache Kafka", "Apache Airflow"],
          alternatives: ["RabbitMQ", "Celery"]
        },
        auth: {
          primary: ["Auth0", "JWT"],
          alternatives: ["Firebase Auth", "Okta"]
        },
        deployment: {
          primary: ["AWS", "Docker", "Kubernetes"],
          alternatives: ["Google Cloud", "Azure"]
        },
        monitoring: {
          primary: ["Grafana", "Prometheus"],
          alternatives: ["DataDog", "New Relic"]
        }
      }
    },
    saas_starter: {
      name: "SaaS Starter Kit",
      description: "Basic SaaS application with user management and subscription billing",
      techStack: {
        frontend: {
          primary: ["Next.js", "React", "TypeScript"],
          styling: ["Tailwind CSS", "Shadcn/ui"],
          stateManagement: ["Zustand", "React Query"],
          alternatives: ["Vue.js", "Svelte"]
        },
        backend: {
          primary: ["Next.js API Routes", "Prisma"],
          alternatives: ["Node.js/Express", "Supabase", "Firebase"]
        },
        database: {
          primary: ["PostgreSQL", "Supabase"],
          alternatives: ["MySQL", "PlanetScale"]
        },
        auth: {
          primary: ["NextAuth.js", "Clerk"],
          alternatives: ["Supabase Auth", "Auth0"]
        },
        payments: {
          primary: ["Stripe", "Lemonsqueezy"],
          alternatives: ["Paddle", "PayPal"]
        },
        deployment: {
          primary: ["Vercel", "Railway"],
          alternatives: ["Netlify", "AWS"]
        },
        email: {
          primary: ["Resend", "SendGrid"],
          alternatives: ["Mailgun", "AWS SES"]
        }
      }
    },
    marketplace: {
      name: "Marketplace Platform",
      description: "Multi-vendor platform connecting buyers and sellers",
      techStack: {
        frontend: {
          primary: ["Next.js", "React", "TypeScript"],
          styling: ["Tailwind CSS", "Framer Motion"],
          stateManagement: ["Zustand", "React Query"],
          alternatives: ["Vue.js", "Angular"]
        },
        backend: {
          primary: ["Node.js", "Express.js", "GraphQL"],
          alternatives: ["Python/Django", "Ruby on Rails"]
        },
        database: {
          primary: ["PostgreSQL", "Redis", "Elasticsearch"],
          alternatives: ["MongoDB", "MySQL"]
        },
        auth: {
          primary: ["Auth0", "Multi-tenant setup"],
          alternatives: ["Firebase Auth", "Okta"]
        },
        payments: {
          primary: ["Stripe Connect", "Split payments"],
          alternatives: ["PayPal Marketplace", "Adyen"]
        },
        search: {
          primary: ["Elasticsearch", "Algolia"],
          alternatives: ["Solr", "Typesense"]
        },
        deployment: {
          primary: ["AWS", "Docker", "Kubernetes"],
          alternatives: ["Google Cloud", "Azure"]
        },
        messaging: {
          primary: ["Socket.io", "Redis Pub/Sub"],
          alternatives: ["Pusher", "AWS SNS"]
        }
      }
    }
  },
  techCategories: {
    frontend: {
      name: "Frontend",
      color: "#3B82F6",
      description: "User interface and client-side logic"
    },
    backend: {
      name: "Backend",
      color: "#10B981",
      description: "Server-side logic and APIs"
    },
    database: {
      name: "Database",
      color: "#F59E0B",
      description: "Data storage and management"
    },
    auth: {
      name: "Authentication",
      color: "#EF4444",
      description: "User authentication and authorization"
    },
    payments: {
      name: "Payments",
      color: "#8B5CF6",
      description: "Payment processing and billing"
    },
    deployment: {
      name: "Deployment",
      color: "#06B6D4",
      description: "Hosting and infrastructure"
    },
    storage: {
      name: "Storage",
      color: "#84CC16",
      description: "File and media storage"
    },
    analytics: {
      name: "Analytics",
      color: "#F97316",
      description: "Data tracking and insights"
    },
    realtime: {
      name: "Real-time",
      color: "#EC4899",
      description: "Live updates and messaging"
    },
    search: {
      name: "Search",
      color: "#6366F1",
      description: "Search and discovery features"
    },
    email: {
      name: "Email",
      color: "#14B8A6",
      description: "Email delivery and notifications"
    },
    monitoring: {
      name: "Monitoring",
      color: "#64748B",
      description: "Application monitoring and logging"
    },
    dataProcessing: {
      name: "Data Processing",
      color: "#DC2626",
      description: "Data pipelines and ETL"
    },
    messaging: {
      name: "Messaging",
      color: "#7C3AED",
      description: "Communication and notifications"
    }
  }
};
