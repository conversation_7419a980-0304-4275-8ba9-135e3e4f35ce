'use client'
import React, { useState } from 'react';
import SaasTypeSelector from '@/components/SaasTypeSelector';
import TechStackVisualizer from '@/components/TechStackVisualizer';
import TechDetailsPanel from '@/components/TechDetailsPanel';
import { techStackData } from '@/lib/techStackData';

export default function TechStackPlanner() {
  const [selectedSaasType, setSelectedSaasType] = useState<string | null>(null);

  const saasTypes = Object.entries(techStackData.saasTypes).map(([id, data]) => ({
    id,
    name: data.name,
    description: data.description,
  }));

  const selectedSaasData = selectedSaasType ? techStackData.saasTypes[selectedSaasType as keyof typeof techStackData.saasTypes] : null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {!selectedSaasType ? (
        <SaasTypeSelector
          saasTypes={saasTypes}
          selectedType={selectedSaasType}
          onTypeSelect={setSelectedSaasType}
        />
      ) : (
        <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
          {/* Left Panel - Tech Details */}
          <div className="w-80 p-4 border-r border-white/30">
            {selectedSaasData && (
              <TechDetailsPanel
                techStack={selectedSaasData.techStack}
                techCategories={techStackData.techCategories}
                saasTypeName={selectedSaasData.name}
                onTechSelect={(tech, category) => {
                  console.log('Selected tech:', tech, 'in category:', category);
                }}
              />
            )}
          </div>

          {/* Right Panel - Visualization */}
          <div className="flex-1 p-6">
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {selectedSaasData?.name} Tech Stack
                  </h1>
                  <p className="text-gray-600">
                    {selectedSaasData?.description}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedSaasType(null)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
                >
                  Change SaaS Type
                </button>
              </div>

              <div className="flex-1">
                {selectedSaasData && (
                  <TechStackVisualizer
                    techStack={selectedSaasData.techStack}
                    techCategories={techStackData.techCategories}
                    saasTypeName={selectedSaasData.name}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}