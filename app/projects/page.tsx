'use client'
import React, { useState, useEffect } from 'react';
import SaasTypeSelector from '@/components/SaasTypeSelector';
import TechStackVisualizer from '@/components/TechStackVisualizer';

interface SaasData {
  saasTypes: {
    [key: string]: {
      name: string;
      description: string;
      techStack: any;
    };
  };
  techCategories: {
    [key: string]: {
      name: string;
      color: string;
      description: string;
    };
  };
}

export default function TechStackPlanner() {
  const [saasData, setSaasData] = useState<SaasData | null>(null);
  const [selectedSaasType, setSelectedSaasType] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load the tech stack data
    fetch('/stack.json')
      .then(response => response.json())
      .then(data => {
        setSaasData(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error loading stack data:', error);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading tech stack data...</div>
      </div>
    );
  }

  if (!saasData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-red-600">Error loading tech stack data</div>
      </div>
    );
  }

  const saasTypes = Object.entries(saasData.saasTypes).map(([id, data]) => ({
    id,
    name: data.name,
    description: data.description,
  }));

  const selectedSaasData = selectedSaasType ? saasData.saasTypes[selectedSaasType] : null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {!selectedSaasType ? (
        <SaasTypeSelector
          saasTypes={saasTypes}
          selectedType={selectedSaasType}
          onTypeSelect={setSelectedSaasType}
        />
      ) : (
        <div className="p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {selectedSaasData?.name} Tech Stack
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  {selectedSaasData?.description}
                </p>
              </div>
              <button
                onClick={() => setSelectedSaasType(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Change SaaS Type
              </button>
            </div>

            {selectedSaasData && (
              <TechStackVisualizer
                techStack={selectedSaasData.techStack}
                techCategories={saasData.techCategories}
                saasTypeName={selectedSaasData.name}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}