'use client'
import React, { useState } from 'react';
import SaasTypeSelector from '@/components/SaasTypeSelector';
import TechStackVisualizer from '@/components/TechStackVisualizer';
import TechDetailsPanel from '@/components/TechDetailsPanel';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Skeleton } from '@/components/ui/skeleton';
import { techStackData } from '@/lib/techStackData';

export default function TechStackPlanner() {
  const [selectedSaasType, setSelectedSaasType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const saasTypes = Object.entries(techStackData.saasTypes).map(([id, data]) => ({
    id,
    name: data.name,
    description: data.description,
  }));

  const selectedSaasData = selectedSaasType ? techStackData.saasTypes[selectedSaasType as keyof typeof techStackData.saasTypes] : null;

  const handleSaasTypeSelect = async (typeId: string) => {
    setIsLoading(true);
    // Simulate loading time for better UX
    await new Promise(resolve => setTimeout(resolve, 500));
    setSelectedSaasType(typeId);
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {!selectedSaasType ? (
        <SaasTypeSelector
          saasTypes={saasTypes}
          selectedType={selectedSaasType}
          onTypeSelect={handleSaasTypeSelect}
        />
      ) : isLoading ? (
        <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
          <div className="w-80 p-4">
            <Skeleton className="w-full h-full rounded-xl" />
          </div>
          <Separator orientation="vertical" className="h-full" />
          <div className="flex-1 p-6">
            <div className="h-full flex flex-col">
              <Skeleton className="h-8 w-64 mb-4" />
              <Skeleton className="h-6 w-96 mb-6" />
              <Skeleton className="flex-1 w-full rounded-xl" />
            </div>
          </div>
        </div>
      ) : (
        <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
          {/* Left Panel - Tech Details */}
          <div className="w-80 p-4">
            {selectedSaasData && (
              <TechDetailsPanel
                techStack={selectedSaasData.techStack}
                techCategories={techStackData.techCategories}
                saasTypeName={selectedSaasData.name}
                onTechSelect={(tech, category) => {
                  console.log('Selected tech:', tech, 'in category:', category);
                }}
              />
            )}
          </div>

          <Separator orientation="vertical" className="h-full" />

          {/* Right Panel - Visualization */}
          <div className="flex-1 p-6">
            <div className="h-full flex flex-col">
              {/* Breadcrumb Navigation */}
              <Breadcrumb className="mb-4">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="#" onClick={() => setSelectedSaasType(null)}>
                      SaaS Types
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{selectedSaasData?.name}</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="flex items-center justify-between mb-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {selectedSaasData?.name} Tech Stack
                  </h1>
                  <p className="text-gray-600">
                    {selectedSaasData?.description}
                  </p>
                </div>
                <Button
                  onClick={() => setSelectedSaasType(null)}
                  variant="outline"
                  size="default"
                >
                  Change SaaS Type
                </Button>
              </div>

              <div className="flex-1">
                {selectedSaasData && (
                  <TechStackVisualizer
                    techStack={selectedSaasData.techStack}
                    techCategories={techStackData.techCategories}
                    saasTypeName={selectedSaasData.name}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}