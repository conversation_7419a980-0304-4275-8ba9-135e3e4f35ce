'use client'

import React from 'react';
import { Handle, Position } from '@xyflow/react';
import { Badge } from '@/components/ui/badge';

interface TechNodeData {
  label: string;
  category: string;
  color: string;
  technologies: string[];
  description?: string;
}

interface TechNodeProps {
  data: TechNodeData;
  selected?: boolean;
}

export function TechCategoryNode({ data, selected }: TechNodeProps) {
  return (
    <div
      className={`px-4 py-3 rounded-xl border min-w-[220px] transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-md ${
        selected
          ? 'bg-white/30 border-white/40 ring-2 ring-white/50 shadow-2xl'
          : 'bg-white/20 border-white/30 hover:bg-white/25 hover:border-white/40 shadow-xl hover:shadow-2xl'
      }`}
      style={{
        borderColor: selected ? data.color : `${data.color}40`,
        boxShadow: selected
          ? `0 25px 50px -12px ${data.color}40, 0 0 0 1px ${data.color}20`
          : `0 20px 25px -5px ${data.color}20, 0 10px 10px -5px ${data.color}10`
      }}
      title={`${data.label}: ${data.description}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 border-2 border-white/50"
        style={{ background: `linear-gradient(135deg, ${data.color}, ${data.color}80)` }}
      />

      <div className="flex items-center gap-2 mb-3">
        <div
          className="w-4 h-4 rounded-full shadow-lg animate-pulse"
          style={{
            background: `linear-gradient(135deg, ${data.color}, ${data.color}80)`,
            boxShadow: `0 0 10px ${data.color}40`
          }}
        />
        <div className="font-bold text-sm text-gray-900 drop-shadow-sm">{data.label}</div>
      </div>

      {data.description && (
        <div className="text-xs text-gray-700 mb-3 line-clamp-2 font-medium">{data.description}</div>
      )}

      <div className="space-y-2">
        {data.technologies.slice(0, 3).map((tech, index) => (
          <div
            key={index}
            className="text-xs px-3 py-1.5 bg-white/40 backdrop-blur-sm rounded-lg text-gray-800 font-medium hover:bg-white/50 transition-all duration-200 border border-white/30"
            title={tech}
          >
            {tech}
          </div>
        ))}
        {data.technologies.length > 3 && (
          <div className="text-xs text-gray-600 font-medium px-2">
            +{data.technologies.length - 3} more technologies
          </div>
        )}
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 border-2 border-white/50"
        style={{ background: `linear-gradient(135deg, ${data.color}, ${data.color}80)` }}
      />
    </div>
  );
}

export function TechDetailNode({ data, selected }: TechNodeProps) {
  return (
    <div
      className={`px-3 py-2 rounded-lg border min-w-[140px] transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-md ${
        selected
          ? 'bg-white/40 border-white/50 ring-1 ring-white/40 shadow-xl'
          : 'bg-white/25 border-white/30 hover:bg-white/35 hover:border-white/40 shadow-lg hover:shadow-xl'
      }`}
      style={{
        boxShadow: selected
          ? `0 10px 25px -5px ${data.color}30, 0 0 0 1px ${data.color}15`
          : `0 8px 15px -3px ${data.color}20`
      }}
      title={`Primary technology: ${data.technologies[0]}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-2 h-2 border border-white/40"
        style={{ background: `linear-gradient(135deg, ${data.color}, ${data.color}80)` }}
      />

      <div className="text-center">
        <div className="font-semibold text-sm text-gray-900 mb-1 drop-shadow-sm">
          {data.label}
        </div>
        <div className="text-xs text-gray-700 font-medium bg-white/30 px-2 py-0.5 rounded-full">
          Primary
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-2 h-2 border border-white/40"
        style={{ background: `linear-gradient(135deg, ${data.color}, ${data.color}80)` }}
      />
    </div>
  );
}

export function AlternativeNode({ data, selected }: TechNodeProps) {
  return (
    <div
      className={`px-2 py-1.5 rounded-md border min-w-[110px] transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-sm ${
        selected
          ? 'bg-gray-100/50 border-gray-300/60 ring-1 ring-gray-200/50 shadow-lg'
          : 'bg-gray-50/40 border-gray-200/50 hover:bg-gray-100/45 hover:border-gray-300/60 shadow-md hover:shadow-lg'
      }`}
      style={{
        boxShadow: selected
          ? '0 8px 20px -5px rgba(107, 114, 128, 0.3)'
          : '0 4px 10px -2px rgba(107, 114, 128, 0.2)'
      }}
      title={`Alternative option: ${data.label}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-1.5 h-1.5 border border-gray-300/50"
        style={{ background: 'linear-gradient(135deg, #9CA3AF, #6B7280)' }}
      />

      <div className="text-center">
        <div className="text-xs text-gray-700 font-medium mb-0.5">
          {data.label}
        </div>
        <div className="text-xs text-gray-500 italic bg-gray-200/40 px-1.5 py-0.5 rounded-full">
          Alternative
        </div>
      </div>
    </div>
  );
}

// Node types for ReactFlow
export const nodeTypes = {
  techCategory: TechCategoryNode,
  techDetail: TechDetailNode,
  alternative: AlternativeNode,
};
