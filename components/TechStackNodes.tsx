'use client'

import React from 'react';
import { Handle, Position } from '@xyflow/react';

interface TechNodeData {
  label: string;
  category: string;
  color: string;
  technologies: string[];
  description?: string;
}

interface TechNodeProps {
  data: TechNodeData;
  selected?: boolean;
}

export function TechCategoryNode({ data, selected }: TechNodeProps) {
  return (
    <div
      className={`px-4 py-3 shadow-lg rounded-lg border-2 bg-white min-w-[200px] transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer ${
        selected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'
      }`}
      style={{ borderColor: selected ? '#3B82F6' : data.color }}
      title={`${data.label}: ${data.description}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3"
        style={{ background: data.color }}
      />

      <div className="flex items-center gap-2 mb-2">
        <div
          className="w-3 h-3 rounded-full animate-pulse"
          style={{ backgroundColor: data.color }}
        />
        <div className="font-bold text-sm text-gray-800">{data.label}</div>
      </div>

      {data.description && (
        <div className="text-xs text-gray-500 mb-2 line-clamp-2">{data.description}</div>
      )}

      <div className="space-y-1">
        {data.technologies.slice(0, 3).map((tech, index) => (
          <div
            key={index}
            className="text-xs px-2 py-1 bg-gray-100 rounded text-gray-700 font-medium hover:bg-gray-200 transition-colors"
            title={tech}
          >
            {tech}
          </div>
        ))}
        {data.technologies.length > 3 && (
          <div className="text-xs text-gray-400 font-medium">
            +{data.technologies.length - 3} more technologies
          </div>
        )}
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3"
        style={{ background: data.color }}
      />
    </div>
  );
}

export function TechDetailNode({ data, selected }: TechNodeProps) {
  return (
    <div
      className={`px-3 py-2 shadow-md rounded-md border bg-white min-w-[120px] transition-all duration-200 hover:shadow-lg hover:scale-105 cursor-pointer ${
        selected ? 'border-blue-400 ring-1 ring-blue-200' : 'border-gray-300 hover:border-gray-400'
      }`}
      title={`Primary technology: ${data.technologies[0]}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-2 h-2"
        style={{ background: data.color }}
      />

      <div className="text-center">
        <div className="font-semibold text-sm text-gray-800 mb-1">
          {data.label}
        </div>
        <div className="text-xs text-gray-600 font-medium">
          Primary Choice
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-2 h-2"
        style={{ background: data.color }}
      />
    </div>
  );
}

export function AlternativeNode({ data, selected }: TechNodeProps) {
  return (
    <div
      className={`px-2 py-1 shadow-sm rounded border bg-gray-50 min-w-[100px] transition-all duration-200 hover:shadow-md hover:bg-gray-100 cursor-pointer ${
        selected ? 'border-blue-300 ring-1 ring-blue-100' : 'border-gray-200 hover:border-gray-300'
      }`}
      title={`Alternative option: ${data.label}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-1.5 h-1.5"
        style={{ background: '#9CA3AF' }}
      />

      <div className="text-center">
        <div className="text-xs text-gray-600 font-medium">
          {data.label}
        </div>
        <div className="text-xs text-gray-400 italic">
          Alternative
        </div>
      </div>
    </div>
  );
}

// Node types for ReactFlow
export const nodeTypes = {
  techCategory: TechCategoryNode,
  techDetail: TechDetailNode,
  alternative: AlternativeNode,
};
