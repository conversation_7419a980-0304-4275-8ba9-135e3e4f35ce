'use client'

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

interface TechStack {
  [category: string]: {
    primary: string[];
    alternatives?: string[];
    [key: string]: any;
  };
}

interface TechCategory {
  name: string;
  color: string;
  description: string;
}

interface TechDetailsPanelProps {
  techStack: TechStack;
  techCategories: { [key: string]: TechCategory };
  saasTypeName: string;
  onTechSelect?: (tech: string, category: string) => void;
}

export default function TechDetailsPanel({ 
  techStack, 
  techCategories, 
  saasTypeName,
  onTechSelect 
}: TechDetailsPanelProps) {
  
  // Get all technologies from the tech stack
  const getAllTechnologies = () => {
    const allTechs: { name: string; category: string; type: 'primary' | 'alternative'; categoryInfo: TechCategory }[] = [];
    
    Object.entries(techStack).forEach(([categoryKey, categoryData]) => {
      const categoryInfo = techCategories[categoryKey];
      if (!categoryInfo) return;
      
      // Add primary technologies
      if (categoryData.primary) {
        categoryData.primary.forEach(tech => {
          allTechs.push({
            name: tech,
            category: categoryKey,
            type: 'primary',
            categoryInfo
          });
        });
      }
      
      // Add alternatives
      if (categoryData.alternatives) {
        categoryData.alternatives.forEach(tech => {
          allTechs.push({
            name: tech,
            category: categoryKey,
            type: 'alternative',
            categoryInfo
          });
        });
      }
      
      // Handle other nested technologies
      Object.entries(categoryData).forEach(([subKey, subData]) => {
        if (subKey !== 'primary' && subKey !== 'alternatives' && Array.isArray(subData)) {
          subData.forEach(tech => {
            if (typeof tech === 'string') {
              allTechs.push({
                name: tech,
                category: categoryKey,
                type: 'primary',
                categoryInfo
              });
            }
          });
        }
      });
    });
    
    return allTechs;
  };

  const technologies = getAllTechnologies();
  const primaryTechs = technologies.filter(t => t.type === 'primary');
  const alternativeTechs = technologies.filter(t => t.type === 'alternative');

  return (
    <Card className="w-80 h-full bg-white/80 backdrop-blur-md border-white/30 shadow-xl">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-bold text-gray-900">
          Tech Stack Details
        </CardTitle>
        <CardDescription className="text-sm text-gray-600">
          {saasTypeName} • {primaryTechs.length} primary technologies
        </CardDescription>
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className="h-[500px] px-6">
          {/* Primary Technologies */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <h3 className="font-semibold text-sm text-gray-900">Primary Technologies</h3>
              <Badge variant="secondary" className="text-xs">
                {primaryTechs.length}
              </Badge>
            </div>
            
            <div className="space-y-3">
              {Object.entries(techCategories).map(([categoryKey, categoryInfo]) => {
                const categoryTechs = primaryTechs.filter(t => t.category === categoryKey);
                if (categoryTechs.length === 0) return null;
                
                return (
                  <div key={categoryKey} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: categoryInfo.color }}
                      />
                      <span className="text-xs font-medium text-gray-700">
                        {categoryInfo.name}
                      </span>
                    </div>
                    
                    <div className="ml-5 space-y-1">
                      {categoryTechs.map((tech, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 rounded-lg bg-white/50 border border-white/30 hover:bg-white/70 transition-colors cursor-pointer"
                          onClick={() => onTechSelect?.(tech.name, tech.category)}
                        >
                          <span className="text-xs font-medium text-gray-800">
                            {tech.name}
                          </span>
                          <Badge 
                            variant="outline" 
                            className="text-xs"
                            style={{ borderColor: categoryInfo.color + '40', color: categoryInfo.color }}
                          >
                            Primary
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          <Separator className="my-4" />
          
          {/* Alternative Technologies */}
          {alternativeTechs.length > 0 && (
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <h3 className="font-semibold text-sm text-gray-900">Alternative Options</h3>
                <Badge variant="outline" className="text-xs">
                  {alternativeTechs.length}
                </Badge>
              </div>
              
              <div className="space-y-3">
                {Object.entries(techCategories).map(([categoryKey, categoryInfo]) => {
                  const categoryTechs = alternativeTechs.filter(t => t.category === categoryKey);
                  if (categoryTechs.length === 0) return null;
                  
                  return (
                    <div key={categoryKey} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full opacity-60"
                          style={{ backgroundColor: categoryInfo.color }}
                        />
                        <span className="text-xs font-medium text-gray-600">
                          {categoryInfo.name} Alternatives
                        </span>
                      </div>
                      
                      <div className="ml-5 space-y-1">
                        {categoryTechs.map((tech, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 rounded-lg bg-gray-50/50 border border-gray-200/50 hover:bg-gray-100/50 transition-colors cursor-pointer"
                            onClick={() => onTechSelect?.(tech.name, tech.category)}
                          >
                            <span className="text-xs font-medium text-gray-700">
                              {tech.name}
                            </span>
                            <Badge variant="outline" className="text-xs text-gray-500 border-gray-300">
                              Alt
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
