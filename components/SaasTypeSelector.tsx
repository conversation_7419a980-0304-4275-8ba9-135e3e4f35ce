'use client'

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface SaasType {
  id: string;
  name: string;
  description: string;
}

interface SaasTypeSelectorProps {
  saasTypes: SaasType[];
  selectedType: string | null;
  onTypeSelect: (typeId: string) => void;
}

export default function SaasTypeSelector({ saasTypes, selectedType, onTypeSelect }: SaasTypeSelectorProps) {
  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          SaaS Tech Stack Planner
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Choose your SaaS type to visualize the recommended tech stack
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {saasTypes.map((type) => (
          <Card
            key={type.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedType === type.id
                ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}
            onClick={() => onTypeSelect(type.id)}
          >
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                {type.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-sm text-gray-600 dark:text-gray-300">
                {type.description}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {selectedType && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Selected: <span className="font-semibold">{saasTypes.find(t => t.id === selectedType)?.name}</span>
          </p>
        </div>
      )}
    </div>
  );
}
