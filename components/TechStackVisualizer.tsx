'use client'

import React, { use<PERSON>allback, useMemo } from 'react';
import { ReactFlow, useNodesState, useEdgesState, Controls, MiniMap, Background, BackgroundVariant } from '@xyflow/react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { nodeTypes } from './TechStackNodes';
import '@xyflow/react/dist/style.css';

interface TechStack {
  [category: string]: {
    primary: string[];
    alternatives?: string[];
    [key: string]: any;
  };
}

interface TechCategory {
  name: string;
  color: string;
  description: string;
}

interface TechStackVisualizerProps {
  techStack: TechStack;
  techCategories: { [key: string]: TechCategory };
  saasTypeName: string;
}

export default function TechStackVisualizer({ 
  techStack, 
  techCategories, 
  saasTypeName 
}: TechStackVisualizerProps) {
  
  const { initialNodes, initialEdges } = useMemo(() => {
    const nodes: any[] = [];
    const edges: any[] = [];
    
    // Create a central node for the SaaS type
    nodes.push({
      id: 'saas-center',
      type: 'input',
      position: { x: 500, y: 80 },
      data: {
        label: saasTypeName,
      },
      style: {
        background: 'linear-gradient(135deg, #1F2937, #374151)',
        color: 'white',
        border: '2px solid rgba(255, 255, 255, 0.2)',
        borderRadius: '12px',
        fontSize: '18px',
        fontWeight: 'bold',
        padding: '12px 24px',
        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
      },
    });
    
    // Position categories in a circle around the center with better spacing
    const categories = Object.keys(techStack);
    const centerX = 600;
    const centerY = 350;
    const radius = 280;
    
    categories.forEach((categoryKey, index) => {
      const category = techStack[categoryKey];
      const categoryInfo = techCategories[categoryKey];
      
      if (!categoryInfo) return;
      
      // Calculate position in circle with better spacing
      const angle = (index * 2 * Math.PI) / categories.length;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      // Get primary technologies
      let primaryTechs: string[] = [];
      if (category.primary) {
        primaryTechs = category.primary;
      } else {
        // Handle cases where the structure might be different
        primaryTechs = Object.values(category).flat().filter(tech => typeof tech === 'string').slice(0, 3);
      }
      
      // Create category node
      nodes.push({
        id: `category-${categoryKey}`,
        type: 'techCategory',
        position: { x: x - 110, y: y - 60 },
        data: {
          label: categoryInfo.name,
          category: categoryKey,
          color: categoryInfo.color,
          technologies: primaryTechs,
          description: categoryInfo.description,
        },
      });
      
      // Create edge from center to category
      edges.push({
        id: `edge-center-${categoryKey}`,
        source: 'saas-center',
        target: `category-${categoryKey}`,
        type: 'smoothstep',
        style: { stroke: categoryInfo.color, strokeWidth: 2 },
        animated: true,
      });
      
      // Create detail nodes for primary technologies
      primaryTechs.slice(0, 2).forEach((tech, techIndex) => {
        const detailX = x + (techIndex - 0.5) * 200;
        const detailY = y + 140;
        
        nodes.push({
          id: `tech-${categoryKey}-${techIndex}`,
          type: 'techDetail',
          position: { x: detailX - 70, y: detailY },
          data: {
            label: tech,
            category: categoryKey,
            color: categoryInfo.color,
            technologies: [tech],
          },
        });
        
        // Create edge from category to tech detail
        edges.push({
          id: `edge-${categoryKey}-tech-${techIndex}`,
          source: `category-${categoryKey}`,
          target: `tech-${categoryKey}-${techIndex}`,
          type: 'smoothstep',
          style: { stroke: categoryInfo.color, strokeWidth: 1 },
        });
      });
      
      // Create alternative nodes if they exist
      if (category.alternatives && category.alternatives.length > 0) {
        category.alternatives.slice(0, 2).forEach((alt, altIndex) => {
          const altX = x + (altIndex - 0.5) * 180;
          const altY = y + 240;
          
          nodes.push({
            id: `alt-${categoryKey}-${altIndex}`,
            type: 'alternative',
            position: { x: altX - 55, y: altY },
            data: {
              label: alt,
              category: categoryKey,
              color: categoryInfo.color,
              technologies: [alt],
            },
          });
          
          // Create edge from category to alternative
          edges.push({
            id: `edge-${categoryKey}-alt-${altIndex}`,
            source: `category-${categoryKey}`,
            target: `alt-${categoryKey}-${altIndex}`,
            type: 'smoothstep',
            style: { stroke: '#9CA3AF', strokeWidth: 1, strokeDasharray: '5,5' },
          });
        });
      }
    });
    
    return { initialNodes: nodes, initialEdges: edges };
  }, [techStack, techCategories, saasTypeName]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: any) => setEdges((eds) => [...eds]),
    [setEdges],
  );

  return (
    <div className="w-full h-full border border-white/30 rounded-xl bg-gradient-to-br from-white/40 via-white/20 to-white/10 backdrop-blur-sm shadow-xl relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        fitViewOptions={{ padding: 0.15 }}
        className="rounded-xl"
      >
        <Controls className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-lg shadow-lg" />
        <MiniMap
          nodeColor={(node) => {
            if (node.type === 'techCategory') return node.data.color as string;
            return '#9CA3AF';
          }}
          className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-lg shadow-lg"
        />
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1.5}
          color="#000000"
          className="opacity-50"
        />
      </ReactFlow>

      {/* Instructions overlay */}
      <Alert className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm border-white/40 shadow-lg max-w-xs">
        <AlertDescription className="text-xs text-gray-700">
          💡 <strong>Tip:</strong> Hover over nodes to see details. Use controls to zoom and pan around the diagram.
        </AlertDescription>
      </Alert>
    </div>
  );
}
