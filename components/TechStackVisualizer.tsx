'use client'

import React, { useCallback, useMemo } from 'react';
import { ReactFlow, useNodesState, useEdgesState, Controls, MiniMap, Background } from '@xyflow/react';
import { nodeTypes } from './TechStackNodes';
import '@xyflow/react/dist/style.css';

interface TechStack {
  [category: string]: {
    primary: string[];
    alternatives?: string[];
    [key: string]: any;
  };
}

interface TechCategory {
  name: string;
  color: string;
  description: string;
}

interface TechStackVisualizerProps {
  techStack: TechStack;
  techCategories: { [key: string]: TechCategory };
  saasTypeName: string;
}

export default function TechStackVisualizer({ 
  techStack, 
  techCategories, 
  saasTypeName 
}: TechStackVisualizerProps) {
  
  const { initialNodes, initialEdges } = useMemo(() => {
    const nodes: any[] = [];
    const edges: any[] = [];
    
    // Create a central node for the SaaS type
    nodes.push({
      id: 'saas-center',
      type: 'input',
      position: { x: 400, y: 50 },
      data: { 
        label: saasTypeName,
      },
      style: {
        background: '#1F2937',
        color: 'white',
        border: '2px solid #374151',
        borderRadius: '8px',
        fontSize: '16px',
        fontWeight: 'bold',
        padding: '10px 20px',
      },
    });
    
    // Position categories in a circle around the center
    const categories = Object.keys(techStack);
    const centerX = 400;
    const centerY = 250;
    const radius = 200;
    
    categories.forEach((categoryKey, index) => {
      const category = techStack[categoryKey];
      const categoryInfo = techCategories[categoryKey];
      
      if (!categoryInfo) return;
      
      // Calculate position in circle
      const angle = (index * 2 * Math.PI) / categories.length;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      // Get primary technologies
      let primaryTechs: string[] = [];
      if (category.primary) {
        primaryTechs = category.primary;
      } else {
        // Handle cases where the structure might be different
        primaryTechs = Object.values(category).flat().filter(tech => typeof tech === 'string').slice(0, 3);
      }
      
      // Create category node
      nodes.push({
        id: `category-${categoryKey}`,
        type: 'techCategory',
        position: { x: x - 100, y: y - 50 },
        data: {
          label: categoryInfo.name,
          category: categoryKey,
          color: categoryInfo.color,
          technologies: primaryTechs,
          description: categoryInfo.description,
        },
      });
      
      // Create edge from center to category
      edges.push({
        id: `edge-center-${categoryKey}`,
        source: 'saas-center',
        target: `category-${categoryKey}`,
        type: 'smoothstep',
        style: { stroke: categoryInfo.color, strokeWidth: 2 },
        animated: true,
      });
      
      // Create detail nodes for primary technologies
      primaryTechs.slice(0, 2).forEach((tech, techIndex) => {
        const detailX = x + (techIndex - 0.5) * 150;
        const detailY = y + 100;
        
        nodes.push({
          id: `tech-${categoryKey}-${techIndex}`,
          type: 'techDetail',
          position: { x: detailX - 60, y: detailY },
          data: {
            label: tech,
            category: categoryKey,
            color: categoryInfo.color,
            technologies: [tech],
          },
        });
        
        // Create edge from category to tech detail
        edges.push({
          id: `edge-${categoryKey}-tech-${techIndex}`,
          source: `category-${categoryKey}`,
          target: `tech-${categoryKey}-${techIndex}`,
          type: 'smoothstep',
          style: { stroke: categoryInfo.color, strokeWidth: 1 },
        });
      });
      
      // Create alternative nodes if they exist
      if (category.alternatives && category.alternatives.length > 0) {
        category.alternatives.slice(0, 2).forEach((alt, altIndex) => {
          const altX = x + (altIndex - 0.5) * 120;
          const altY = y + 180;
          
          nodes.push({
            id: `alt-${categoryKey}-${altIndex}`,
            type: 'alternative',
            position: { x: altX - 50, y: altY },
            data: {
              label: alt,
              category: categoryKey,
              color: categoryInfo.color,
              technologies: [alt],
            },
          });
          
          // Create edge from category to alternative
          edges.push({
            id: `edge-${categoryKey}-alt-${altIndex}`,
            source: `category-${categoryKey}`,
            target: `alt-${categoryKey}-${altIndex}`,
            type: 'smoothstep',
            style: { stroke: '#9CA3AF', strokeWidth: 1, strokeDasharray: '5,5' },
          });
        });
      }
    });
    
    return { initialNodes: nodes, initialEdges: edges };
  }, [techStack, techCategories, saasTypeName]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: any) => setEdges((eds) => [...eds]),
    [setEdges],
  );

  return (
    <div className="w-full h-[600px] border border-gray-200 rounded-lg bg-gray-50">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        fitViewOptions={{ padding: 0.1 }}
      >
        <Controls />
        <MiniMap 
          nodeColor={(node) => {
            if (node.type === 'techCategory') return node.data.color;
            return '#9CA3AF';
          }}
        />
        <Background variant="dots" gap={12} size={1} />
      </ReactFlow>
    </div>
  );
}
